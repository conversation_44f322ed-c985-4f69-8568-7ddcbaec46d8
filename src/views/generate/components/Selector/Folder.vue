<template>
    <PopupPanel :title="`选择文件夹`" @close="emit('close')">
        <div class="popup-body">
            <div class="popup-row">
                <t-input
                    v-model="searchKeyword"
                    placeholder="搜索文件夹..."
                    clearable
                    @input="handleSearch"
                >
                    <template #suffixIcon>
                        <SearchIcon />
                    </template>
                </t-input>

                <div class="search-history-panel" v-if="storageKey && historyList.length">
                    <span class="history-title">历史搜索</span>

                    <div class="search-history-list">
                        <div class="history-item" v-for="item in historyList" :key="item.id" @click="handleHistoryClick(item.id)">{{ item.title }}</div>
                    </div>
                </div>
            </div>

            <div class="popup-row tree-wrapper">
                <div class="tree-tools">
                    <t-checkbox :checked="checkAll" :indeterminate="indeterminate" @change="handleSelectAll">全选</t-checkbox>
                </div>
                {{checkedKeys}}

                <t-tree
                    :data="filteredOptions"
                    :keys="{ value: 'id', label: 'name', children: 'children' }"
                    :checkable="true"
                    :checked="checkedKeys"
                    :expanded="expandedKeys"
                    @check="handleTreeCheck"
                    @expand="handleTreeExpand"
                >
                    <template #label="{ node }">
                        <span class="tree-node-label">
                            {{ node.data.name }}
                            <t-badge v-if="getNodeSelectedCount(node.data)" :count="getNodeSelectedCount(node.data)" />
                        </span>
                    </template>
                </t-tree>
            </div>
        </div>

        <template #footer>
            <div class="popup-panel-footer-container">
                <div class="popup-panel-footer-more">
                    <div class="item">
                        <span class="title required">素材规格</span>

                        <t-space size="small">
                            <t-button size="small" v-for="item in verticalOptions" :key="item.value" :theme="verticalValue === item.value ? 'primary' : 'default'" @click="verticalValue = item.value">
                                {{ item.label }}
                            </t-button>
                        </t-space>
                    </div>

                    <t-divider layout="vertical" />

                    <div class="item">
                        <span class="title">开启素材原声</span>
                        <t-switch size="large" v-model="originAudioValue"></t-switch>
                    </div>
                </div>

                <div class="popup-panel-footer-buttons">
                    <t-space>
                        <t-button theme="default" @click="emit('close')">取消</t-button>
                        <t-button theme="primary" @click="handleConfirm">确定</t-button>
                    </t-space>
                </div>
            </div>
        </template>
    </PopupPanel>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
import { SearchIcon } from "tdesign-icons-vue-next";
import { cloneDeep } from "lodash";

import { useHistoryStore, ObjectHistoryKey } from "@/store/history";
import { treeToMap } from "@/utils/fn";
import { PromptMap } from "../../common";
import { verticalOptions } from "../../common/fields";
import { getFolderList } from "@/api/folder";

import PopupPanel from "../PopupPanel.vue";

// 文件夹类型定义
interface Folder {
    id: string;
    name: string;
    pid?: string;
    children?: Folder[];
}

const props = defineProps<{
    value: PromptMap["categoryIds"];
    vertical: PromptMap["vertical"];
    originAudio: PromptMap["originAudio"];
    storageKey?: ObjectHistoryKey;
}>();

const emit = defineEmits<{
    (e: "close"): void;
    (e: "confirm", event: { value: PromptMap["categoryIds"]; vertical: PromptMap["vertical"]; originAudio: PromptMap["originAudio"] }): void;
}>();

// 历史记录
const historyStore = useHistoryStore();

// 文件夹数据
const options = ref<Folder[]>([]);
const optionsMap = computed(() => treeToMap<Folder>(options.value, { parentIdKey: "pid", idKey: "id" }));

// 历史记录列表
const historyList = computed(() => (props.storageKey ? historyStore[props.storageKey].filter((v) => optionsMap.value.get(v.id)).filter((_, i) => i < 6) : []));

// 素材规格
const verticalValue = ref(props.vertical);
// 素材原声
const originAudioValue = ref(props.originAudio);

// 搜索相关
const searchKeyword = ref("");

// 树形控件相关
const checkedKeys = ref<string[]>([]);
const expandedKeys = ref<string[]>([]);

// 过滤后的文件夹数据
const filteredOptions = computed(() => {
    if (!searchKeyword.value.trim()) {
        return options.value;
    }

    const keyword = searchKeyword.value.toLowerCase();

    // 递归过滤函数
    const filterTree = (nodes: Folder[]): Folder[] => {
        return nodes.reduce((filtered: Folder[], node) => {
            const matchesKeyword = node.name.toLowerCase().includes(keyword);
            const filteredChildren = node.children ? filterTree(node.children) : [];

            // 如果当前节点匹配或有匹配的子节点，则包含此节点
            if (matchesKeyword || filteredChildren.length > 0) {
                filtered.push({
                    ...node,
                    children: filteredChildren.length > 0 ? filteredChildren : node.children
                });
            }

            return filtered;
        }, []);
    };

    return filterTree(options.value);
});

// 获取文件夹数据
onMounted(async () => {
    try {
        const res = await getFolderList();
        options.value = res;

        // 初始化选中的keys
        checkedKeys.value = props.value.map(item => item.value);

        // 默认展开第一层
        expandedKeys.value = res.map(item => item.id);
    } catch (error) {
        console.error('获取文件夹列表失败:', error);
    }
});

// 搜索处理
const handleSearch = () => {
    // 搜索时自动展开所有匹配的节点
    if (searchKeyword.value.trim()) {
        const expandAll = (nodes: Folder[]): string[] => {
            let ids: string[] = [];
            nodes.forEach(node => {
                ids.push(node.id);
                if (node.children) {
                    ids.push(...expandAll(node.children));
                }
            });
            return ids;
        };
        expandedKeys.value = expandAll(filteredOptions.value);
    } else {
        // 清空搜索时恢复默认展开状态
        expandedKeys.value = options.value.map(item => item.id);
    }
};

// 历史记录点击处理
const handleHistoryClick = (id: string) => {
    const item = optionsMap.value.get(id);
    if (item) {
        // 设置搜索关键词为历史项的名称
        searchKeyword.value = item.name;
        handleSearch();

        // 添加到历史记录
        if (props.storageKey) {
            historyStore.addObjectHistory(props.storageKey, { id: item.id, title: item.name });
        }
    }
};

// 树形控件选中事件
const handleTreeCheck = (checkedValues: string[]) => {
    console.log(checkedKeys.value);
    checkedKeys.value = checkedValues;

    // 更新选中的文件夹映射
    selectedFolderMap.clear();
    console.log(checkedValues);
    checkedValues.forEach(id => {
        const item = optionsMap.value.get(id);
        if (item) {
            selectedFolderMap.set(id, { value: id, title: item.name, pid: item.pid });
        }
    });
};

// 树形控件展开事件
const handleTreeExpand = (expandedValues: string[]) => {
    expandedKeys.value = expandedValues;
};

const selectedFolderMap = reactive(new Map(props.value?.map((item) => [item.value, item])));

const selectedFolders = computed<PromptMap["categoryIds"]>(() => Array.from(selectedFolderMap.values()));

// 获取节点选中数量（用于显示徽章）
const getNodeSelectedCount = (node: Folder) => {
    const getAllChildIds = (folder: Folder): string[] => {
        let ids = [folder.id];
        if (folder.children) {
            folder.children.forEach(child => {
                ids.push(...getAllChildIds(child));
            });
        }
        return ids;
    };

    const childIds = getAllChildIds(node);
    return selectedFolders.value.filter(item => childIds.includes(item.value)).length;
};

// 获取所有节点ID
const getAllNodeIds = (nodes: Folder[]): string[] => {
    let ids: string[] = [];
    nodes.forEach(node => {
        ids.push(node.id);
        if (node.children) {
            ids.push(...getAllNodeIds(node.children));
        }
    });
    return ids;
};

// 基于过滤后的数据计算全选状态
const filteredNodeIds = computed(() => getAllNodeIds(filteredOptions.value));
const checkAll = computed(() => filteredNodeIds.value.length > 0 && filteredNodeIds.value.every(id => checkedKeys.value.includes(id)));
const indeterminate = computed(() => !checkAll.value && filteredNodeIds.value.some(id => checkedKeys.value.includes(id)));

const handleSelectAll = (checked: boolean) => {
    if (checked) {
        // 全选：选中当前过滤结果中的所有节点
        const newCheckedKeys = [...new Set([...checkedKeys.value, ...filteredNodeIds.value])];
        checkedKeys.value = newCheckedKeys;

        // 更新选中的文件夹映射
        filteredNodeIds.value.forEach(id => {
            const item = optionsMap.value.get(id);
            if (item && !selectedFolderMap.has(id)) {
                selectedFolderMap.set(id, { value: id, title: item.name, pid: item.pid });
            }
        });
    } else {
        // 取消全选：移除当前过滤结果中的所有选中节点
        checkedKeys.value = checkedKeys.value.filter(id => !filteredNodeIds.value.includes(id));

        // 更新选中的文件夹映射
        filteredNodeIds.value.forEach(id => {
            selectedFolderMap.delete(id);
        });
    }
};

const handleConfirm = () => {
    emit("confirm", { value: cloneDeep(selectedFolders.value), vertical: verticalValue.value, originAudio: originAudioValue.value });
    emit("close");
};
</script>

<style lang="less" scoped>
@import "../../common/css/popup.less";

.search-history-panel {
    user-select: none;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;

    > .history-title {
        color: #808695;
        font-size: 12px;
        margin-right: 8px;
    }

    > .search-history-list {
        flex: 1;

        white-space: nowrap;
        overflow-x: auto;

        > .history-item {
            display: inline-block;
            align-items: center;
            padding: 4px 8px;
            background-color: #363e4f;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.2s;

            color: #c5c6c9;
            font-size: 12px;

            & + .history-item {
                margin-left: 8px;
            }

            &:hover {
                background-color: #404b61;
            }
        }
    }
}

.tree-wrapper {
    flex: 1;
    overflow: hidden;
    border-radius: 12px;
    background-color: #242b33;
    padding: 20px;

    .tree-tools {
        text-align: right;
        padding-bottom: 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        margin-bottom: 16px;
    }

    .tree-node-label {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    :deep(.t-tree) {
        background-color: transparent;
        color: #ffffff;

        .t-tree__list {
            background-color: transparent;
        }

        .t-tree__item {
            color: #ffffff;

            &:hover {
                background-color: rgba(255, 255, 255, 0.05);
            }

            .t-tree__item--active {
                background-color: rgba(0, 82, 217, 0.1);
            }
        }

        .t-tree__label {
            color: #ffffff;
        }

        .t-tree__icon {
            color: #ffffff;
        }

        .t-checkbox {
            .t-checkbox__input {
                border-color: #404b61;
            }

            &.t-is-checked .t-checkbox__input {
                background-color: #0052d9;
                border-color: #0052d9;
            }
        }
    }
}

.popup-panel-footer-container {
    padding-left: 0 !important;

    > .popup-panel-footer-more {
        display: flex;
        align-items: center;

        > .item {
            > .title {
                margin-right: 10px;

                font-weight: 400;
                font-size: 12px;
                line-height: 22px;

                &.required:before {
                    content: "* ";
                    color: rgba(234, 0, 0, 1);
                }
            }
        }
    }
}
</style>
